// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "AppDelegate.h"
#include "DummyPlatformView.h"
#include "GeneratedPluginRegistrant.h"

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
  [GeneratedPluginRegistrant registerWithRegistry:self];

   NSObject<FlutterPluginRegistrar>* registrar =
      [self registrarForPlugin:@"benchmarks/platform_views_layout_hybrid_composition/DummyPlatformViewPlugin"];

  DummyPlatformViewFactory* dummyPlatformViewFactory = [[DummyPlatformViewFactory alloc] init];
  [registrar registerViewFactory:dummyPlatformViewFactory
                                withId:@"benchmarks/platform_views_layout_hybrid_composition/DummyPlatformView"
      gestureRecognizersBlockingPolicy:FlutterPlatformViewGestureRecognizersBlockingPolicyEager];

  // Override point for customization after application launch.
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

@end
