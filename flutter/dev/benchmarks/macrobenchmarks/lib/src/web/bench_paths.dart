// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'bench_paths_recording.dart' as recording;
import 'recorder.dart';

/// Measure the performance of path construction.
///
/// This benchmarks was generated by running flutter gallery and recording
/// path calls.
class BenchPathRecording extends RawRecorder {
  BenchPathRecording() : super(name: benchmarkName);

  static const String benchmarkName = 'bench_path_recording';

  @override
  Future<void> setUpAll() async {
  }

  @override
  void body(Profile profile) {
    profile.record('recordPathConstruction', () {
      for (int i = 1; i <= 10; i++) {
        recording.createPaths();
      }
    }, reported: true);
  }
}
