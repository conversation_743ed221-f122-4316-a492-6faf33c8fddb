{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "flutter_sound", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": []}, {"name": "integration_test", "path": "/Users/<USER>/flutter/packages/integration_test/", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"]}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": []}, {"name": "siri_shortcuts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/siri_shortcuts-0.0.1/", "native_build": true, "dependencies": []}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/", "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "android_intent_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3/", "native_build": true, "dependencies": []}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "flutter_sound", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": []}, {"name": "integration_test", "path": "/Users/<USER>/flutter/packages/integration_test/", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/", "native_build": true, "dependencies": ["audio_session"]}, {"name": "local_auth_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.47/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": []}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/", "native_build": true, "dependencies": []}], "macos": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": []}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"]}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "siri_shortcuts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/siri_shortcuts-0.0.1/", "native_build": true, "dependencies": []}, {"name": "speech_to_text_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/", "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": false, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}], "windows": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "native_build": true, "dependencies": []}, {"name": "local_auth_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": []}], "web": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "dependencies": []}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "dependencies": []}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "dependencies": []}, {"name": "flutter_secure_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": []}, {"name": "flutter_sound_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/", "dependencies": []}, {"name": "flutter_tts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/", "dependencies": []}, {"name": "just_audio_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/", "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "dependencies": []}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": []}, {"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/", "dependencies": []}]}, "dependencyGraph": [{"name": "android_intent_plus", "dependencies": []}, {"name": "audio_session", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "flutter_sound", "dependencies": ["path_provider", "flutter_sound_web"]}, {"name": "flutter_sound_web", "dependencies": []}, {"name": "flutter_tts", "dependencies": []}, {"name": "integration_test", "dependencies": []}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "siri_shortcuts", "dependencies": []}, {"name": "speech_to_text", "dependencies": ["speech_to_text_macos"]}, {"name": "speech_to_text_macos", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}], "date_created": "2025-10-06 13:28:37.164318", "version": "3.24.5", "swift_package_manager_enabled": false}