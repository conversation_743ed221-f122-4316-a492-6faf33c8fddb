# This is a generated file; do not edit or check into version control.
android_intent_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3/
audio_session=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
flutter_sound=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/
flutter_sound_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/
flutter_tts=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_tts-3.8.5/
integration_test=/Users/<USER>/flutter/packages/integration_test/
just_audio=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/
just_audio_web=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/
local_auth=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/
local_auth_android=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.47/
local_auth_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/
local_auth_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
siri_shortcuts=/Users/<USER>/.pub-cache/hosted/pub.dev/siri_shortcuts-0.0.1/
speech_to_text=/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.0/
speech_to_text_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
